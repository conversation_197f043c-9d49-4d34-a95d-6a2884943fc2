#include <stdio.h>
#include <stdlib.h>
#include <math.h>
#include <float.h>

// 二维点结构体，简单明了
typedef struct {
    double x, y;
} Point;

// BST节点结构体，包含点数据和左右子树
typedef struct Node {
    Point pt;           // 存储的点
    struct Node *lft;   // 左子树
    struct Node *rgt;   // 右子树
} Node;

// 计算两点间的欧几里得距离，基础几何
double calc_dist(Point p1, Point p2) {
    double dx, dy;
    dx = p1.x - p2.x;
    dy = p1.y - p2.y;
    return sqrt(dx * dx + dy * dy);
}

// 创建新节点，分配内存并初始化
Node* create_node(Point pt) {
    Node *nd;
    nd = (Node*)malloc(sizeof(Node));
    if (nd == NULL) {
        printf("内存分配失败了，程序要挂了\n");
        exit(1);
    }
    nd->pt = pt;
    nd->lft = NULL;
    nd->rgt = NULL;
    return nd;
}

// 向BST插入新点，按x坐标排序
Node* insert_point(Node *root, Point pt) {
    // 如果是空树，直接创建根节点
    if (root == NULL) {
        return create_node(pt);
    }
    
    // 按x坐标比较，决定插入左边还是右边
    if (pt.x < root->pt.x) {
        root->lft = insert_point(root->lft, pt);
    } else {
        root->rgt = insert_point(root->rgt, pt);
    }
    
    return root;
}

// 最近邻搜索的核心函数，递归查找
void find_nearest(Node *root, Point query, Node **best, double *min_dst) {
    double cur_dst, dx;
    
    // 空节点就不用找了
    if (root == NULL) return;
    
    // 计算当前节点到查询点的距离
    cur_dst = calc_dist(root->pt, query);
    
    // 如果找到更近的点，就更新最佳结果
    if (cur_dst < *min_dst) {
        *min_dst = cur_dst;
        *best = root;
    }
    
    // 计算x方向的距离，用于剪枝判断
    dx = query.x - root->pt.x;
    
    // 先搜索更可能包含最近点的一边
    if (dx < 0) {
        // 查询点在左边，先搜左子树
        find_nearest(root->lft, query, best, min_dst);
        // 如果右边可能有更近的点，也要搜一下
        if (fabs(dx) < *min_dst) {
            find_nearest(root->rgt, query, best, min_dst);
        }
    } else {
        // 查询点在右边，先搜右子树
        find_nearest(root->rgt, query, best, min_dst);
        // 如果左边可能有更近的点，也要搜一下
        if (fabs(dx) < *min_dst) {
            find_nearest(root->lft, query, best, min_dst);
        }
    }
}

// 对外接口，查找最近邻点
Node* nearest_neighbor(Node *root, Point query) {
    Node *best;
    double min_dst;
    
    if (root == NULL) {
        printf("树是空的，找不到任何点\n");
        return NULL;
    }
    
    best = root;
    min_dst = DBL_MAX;  // 初始化为最大值
    
    find_nearest(root, query, &best, &min_dst);
    return best;
}

// 打印点的坐标，方便调试
void print_point(Point pt) {
    printf("(%.2f, %.2f)", pt.x, pt.y);
}

// 释放BST内存，避免内存泄漏
void free_tree(Node *root) {
    if (root == NULL) return;
    free_tree(root->lft);
    free_tree(root->rgt);
    free(root);
}

// 主函数，测试我们的BST最近邻搜索
int main() {
    Node *root, *nearest;
    Point pts[] = {{2.0, 3.0}, {5.0, 4.0}, {9.0, 6.0}, {4.0, 7.0}, {8.0, 1.0}, {7.0, 2.0}};
    Point query;
    int i, n;
    
    root = NULL;
    n = sizeof(pts) / sizeof(pts[0]);
    
    printf("开始构建BST，插入%d个点...\n", n);
    
    // 把所有点插入到BST中
    for (i = 0; i < n; i++) {
        root = insert_point(root, pts[i]);
        printf("插入点 ");
        print_point(pts[i]);
        printf("\n");
    }
    
    printf("\n请输入查询点的坐标 (x y): ");
    if (scanf("%lf %lf", &query.x, &query.y) != 2) {
        printf("输入格式不对，程序退出\n");
        free_tree(root);
        return 1;
    }
    
    printf("查询点: ");
    print_point(query);
    printf("\n");
    
    // 查找最近邻
    nearest = nearest_neighbor(root, query);
    
    if (nearest != NULL) {
        printf("找到最近的点: ");
        print_point(nearest->pt);
        printf("，距离为: %.2f\n", calc_dist(query, nearest->pt));
    } else {
        printf("没找到任何点\n");
    }
    
    // 清理内存
    free_tree(root);
    
    return 0;
}
